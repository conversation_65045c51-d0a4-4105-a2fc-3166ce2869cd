# 卡牌集社模块MySQL查询性能分析

## 环境准备

### 1. 连接MySQL数据库
```bash
# 连接到本地MySQL数据库
mysql -u root -p

# 选择数据库
USE your_database_name;
```

### 2. 创建测试数据（可选）
```sql
-- 如果需要测试数据，可以插入一些示例数据
-- 这里假设表已经存在，只需要确保有足够的测试数据
```

## 用户端核心查询EXPLAIN分析

### 1. 帖子列表查询

#### 优化前查询
```sql
-- 查询：获取已上架帖子列表
EXPLAIN SELECT * FROM posts 
WHERE status = 1 
ORDER BY created_at DESC 
LIMIT 20;
```

**预期结果分析**：
- **type**: ALL (全表扫描)
- **key**: NULL (未使用索引)
- **rows**: 表中所有记录数
- **Extra**: Using where; Using filesort

#### 优化后查询（添加idx_status_created_at索引后）
```sql
-- 先添加索引
ALTER TABLE `posts` ADD INDEX `idx_status_created_at` (`status`, `created_at` DESC);

-- 再次执行查询
EXPLAIN SELECT * FROM posts 
WHERE status = 1 
ORDER BY created_at DESC 
LIMIT 20;
```

**预期结果分析**：
- **type**: ref
- **key**: idx_status_created_at
- **rows**: 大幅减少（只扫描status=1的记录）
- **Extra**: Using index condition (无需filesort)

### 2. 我的帖子查询

#### 优化前查询
```sql
-- 查询：获取指定商家的帖子
EXPLAIN SELECT * FROM posts 
WHERE merchant_id = 'merchant_123' AND status IN (1, -2, -3)
ORDER BY created_at DESC 
LIMIT 20;
```

#### 优化后查询
```sql
-- 添加复合索引
ALTER TABLE `posts` ADD INDEX `idx_merchant_status_created_at` (`merchant_id`, `status`, `created_at` DESC);

-- 再次执行查询
EXPLAIN SELECT * FROM posts 
WHERE merchant_id = 'merchant_123' AND status IN (1, -2, -3)
ORDER BY created_at DESC 
LIMIT 20;
```

### 3. 会话列表查询

#### 优化前查询
```sql
-- 查询：获取用户会话列表
EXPLAIN SELECT * FROM conversations 
WHERE participant_id = 'user_123' AND is_deleted = 0 
ORDER BY last_message_time DESC, created_at DESC 
LIMIT 20;
```

#### 优化后查询
```sql
-- 添加复合索引
ALTER TABLE `conversations` ADD INDEX `idx_participant_deleted_time` (`participant_id`, `is_deleted`, `last_message_time` DESC, `created_at` DESC);

-- 再次执行查询
EXPLAIN SELECT * FROM conversations 
WHERE participant_id = 'user_123' AND is_deleted = 0 
ORDER BY last_message_time DESC, created_at DESC 
LIMIT 20;
```

### 4. 消息列表查询

#### 优化前查询
```sql
-- 查询：获取两个用户之间的消息
EXPLAIN SELECT * FROM messages 
WHERE big_user_id = 'user_456' AND small_user_id = 'user_123'
ORDER BY client_msg_number DESC, created_at DESC 
LIMIT 50;
```

#### 优化后查询
```sql
-- 添加复合索引
ALTER TABLE `messages` ADD INDEX `idx_users_order` (`big_user_id`, `small_user_id`, `client_msg_number` DESC, `created_at` DESC);

-- 再次执行查询
EXPLAIN SELECT * FROM messages 
WHERE big_user_id = 'user_456' AND small_user_id = 'user_123'
ORDER BY client_msg_number DESC, created_at DESC 
LIMIT 50;
```

## 执行脚本

### 完整的EXPLAIN分析脚本
```bash
#!/bin/bash

# MySQL连接参数
MYSQL_HOST="localhost"
MYSQL_USER="root"
MYSQL_PASSWORD="your_password"
MYSQL_DATABASE="your_database"

# 输出文件
OUTPUT_FILE="explain_analysis_results.txt"

echo "=== 卡牌集社模块查询性能分析 ===" > $OUTPUT_FILE
echo "分析时间: $(date)" >> $OUTPUT_FILE
echo "" >> $OUTPUT_FILE

# 函数：执行EXPLAIN并记录结果
run_explain() {
    local query_name="$1"
    local sql_query="$2"
    
    echo "### $query_name" >> $OUTPUT_FILE
    echo "查询语句:" >> $OUTPUT_FILE
    echo "$sql_query" >> $OUTPUT_FILE
    echo "" >> $OUTPUT_FILE
    echo "EXPLAIN结果:" >> $OUTPUT_FILE
    
    mysql -h$MYSQL_HOST -u$MYSQL_USER -p$MYSQL_PASSWORD $MYSQL_DATABASE -e "EXPLAIN $sql_query" >> $OUTPUT_FILE 2>&1
    
    echo "" >> $OUTPUT_FILE
    echo "---" >> $OUTPUT_FILE
    echo "" >> $OUTPUT_FILE
}

# 1. 帖子列表查询（优化前）
run_explain "1. 帖子列表查询（优化前）" "SELECT * FROM posts WHERE status = 1 ORDER BY created_at DESC LIMIT 20;"

# 添加索引
mysql -h$MYSQL_HOST -u$MYSQL_USER -p$MYSQL_PASSWORD $MYSQL_DATABASE -e "ALTER TABLE posts ADD INDEX idx_status_created_at (status, created_at DESC);" 2>/dev/null

# 1. 帖子列表查询（优化后）
run_explain "1. 帖子列表查询（优化后）" "SELECT * FROM posts WHERE status = 1 ORDER BY created_at DESC LIMIT 20;"

# 2. 我的帖子查询（优化前）
run_explain "2. 我的帖子查询（优化前）" "SELECT * FROM posts WHERE merchant_id = 'merchant_123' AND status IN (1, -2, -3) ORDER BY created_at DESC LIMIT 20;"

# 添加索引
mysql -h$MYSQL_HOST -u$MYSQL_USER -p$MYSQL_PASSWORD $MYSQL_DATABASE -e "ALTER TABLE posts ADD INDEX idx_merchant_status_created_at (merchant_id, status, created_at DESC);" 2>/dev/null

# 2. 我的帖子查询（优化后）
run_explain "2. 我的帖子查询（优化后）" "SELECT * FROM posts WHERE merchant_id = 'merchant_123' AND status IN (1, -2, -3) ORDER BY created_at DESC LIMIT 20;"

# 3. 会话列表查询（优化前）
run_explain "3. 会话列表查询（优化前）" "SELECT * FROM conversations WHERE participant_id = 'user_123' AND is_deleted = 0 ORDER BY last_message_time DESC, created_at DESC LIMIT 20;"

# 添加索引
mysql -h$MYSQL_HOST -u$MYSQL_USER -p$MYSQL_PASSWORD $MYSQL_DATABASE -e "ALTER TABLE conversations ADD INDEX idx_participant_deleted_time (participant_id, is_deleted, last_message_time DESC, created_at DESC);" 2>/dev/null

# 3. 会话列表查询（优化后）
run_explain "3. 会话列表查询（优化后）" "SELECT * FROM conversations WHERE participant_id = 'user_123' AND is_deleted = 0 ORDER BY last_message_time DESC, created_at DESC LIMIT 20;"

# 4. 消息列表查询（优化前）
run_explain "4. 消息列表查询（优化前）" "SELECT * FROM messages WHERE big_user_id = 'user_456' AND small_user_id = 'user_123' ORDER BY client_msg_number DESC, created_at DESC LIMIT 50;"

# 添加索引
mysql -h$MYSQL_HOST -u$MYSQL_USER -p$MYSQL_PASSWORD $MYSQL_DATABASE -e "ALTER TABLE messages ADD INDEX idx_users_order (big_user_id, small_user_id, client_msg_number DESC, created_at DESC);" 2>/dev/null

# 4. 消息列表查询（优化后）
run_explain "4. 消息列表查询（优化后）" "SELECT * FROM messages WHERE big_user_id = 'user_456' AND small_user_id = 'user_123' ORDER BY client_msg_number DESC, created_at DESC LIMIT 50;"

echo "分析完成，结果保存在 $OUTPUT_FILE"
```

## 手动执行步骤

### 1. 连接数据库并执行分析
```bash
# 1. 连接MySQL
mysql -u root -p

# 2. 选择数据库
USE your_database_name;

# 3. 执行EXPLAIN分析
```

### 2. 逐个查询分析
```sql
-- 帖子列表查询分析
EXPLAIN SELECT * FROM posts WHERE status = 1 ORDER BY created_at DESC LIMIT 20;

-- 添加索引后再次分析
ALTER TABLE `posts` ADD INDEX `idx_status_created_at` (`status`, `created_at` DESC);
EXPLAIN SELECT * FROM posts WHERE status = 1 ORDER BY created_at DESC LIMIT 20;

-- 我的帖子查询分析
EXPLAIN SELECT * FROM posts WHERE merchant_id = 'test_merchant' AND status IN (1, -2, -3) ORDER BY created_at DESC LIMIT 20;

-- 添加索引后再次分析
ALTER TABLE `posts` ADD INDEX `idx_merchant_status_created_at` (`merchant_id`, `status`, `created_at` DESC);
EXPLAIN SELECT * FROM posts WHERE merchant_id = 'test_merchant' AND status IN (1, -2, -3) ORDER BY created_at DESC LIMIT 20;

-- 会话列表查询分析
EXPLAIN SELECT * FROM conversations WHERE participant_id = 'test_user' AND is_deleted = 0 ORDER BY last_message_time DESC, created_at DESC LIMIT 20;

-- 添加索引后再次分析
ALTER TABLE `conversations` ADD INDEX `idx_participant_deleted_time` (`participant_id`, `is_deleted`, `last_message_time` DESC, `created_at` DESC);
EXPLAIN SELECT * FROM conversations WHERE participant_id = 'test_user' AND is_deleted = 0 ORDER BY last_message_time DESC, created_at DESC LIMIT 20;

-- 消息列表查询分析
EXPLAIN SELECT * FROM messages WHERE big_user_id = 'user_big' AND small_user_id = 'user_small' ORDER BY client_msg_number DESC, created_at DESC LIMIT 50;

-- 添加索引后再次分析
ALTER TABLE `messages` ADD INDEX `idx_users_order` (`big_user_id`, `small_user_id`, `client_msg_number` DESC, `created_at` DESC);
EXPLAIN SELECT * FROM messages WHERE big_user_id = 'user_big' AND small_user_id = 'user_small' ORDER BY client_msg_number DESC, created_at DESC LIMIT 50;
```

## EXPLAIN结果解读

### 关键字段说明
- **id**: 查询序列号
- **select_type**: 查询类型（SIMPLE、PRIMARY等）
- **table**: 查询的表名
- **type**: 连接类型（ALL、index、range、ref、eq_ref、const等）
- **possible_keys**: 可能使用的索引
- **key**: 实际使用的索引
- **key_len**: 使用索引的长度
- **ref**: 索引的哪一列被使用
- **rows**: 估算需要扫描的行数
- **Extra**: 额外信息（Using index、Using filesort、Using temporary等）

### 性能优化指标
- **type从ALL改为ref/range**: 表示索引生效
- **rows数量大幅减少**: 表示扫描行数减少
- **Extra去掉Using filesort**: 表示利用索引排序，无需额外排序操作
- **key显示新建索引名**: 表示新索引被使用

## 预期优化效果

### 1. 帖子列表查询
- **优化前**: 全表扫描 + 文件排序
- **优化后**: 索引扫描 + 索引排序
- **性能提升**: 查询时间从O(n log n)降至O(log n)

### 2. 我的帖子查询  
- **优化前**: 全表扫描 + 条件过滤 + 文件排序
- **优化后**: 复合索引精确定位 + 索引排序
- **性能提升**: 查询时间大幅降低，特别是数据量大时

### 3. 会话列表查询
- **优化前**: 全表扫描 + 多条件过滤 + 文件排序
- **优化后**: 复合索引快速定位 + 索引排序
- **性能提升**: 支持高并发用户会话查询

### 4. 消息列表查询
- **优化前**: 全表扫描 + 双条件过滤 + 文件排序
- **优化后**: 复合索引精确匹配 + 索引排序
- **性能提升**: 消息查询响应时间显著降低
