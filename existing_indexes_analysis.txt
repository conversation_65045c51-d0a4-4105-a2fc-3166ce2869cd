=== 卡牌集社模块查询性能分析（索引已存在） ===
分析时间: 2025-08-05 15:32:55
数据库: app_service
主机: 127.0.0.1

### posts 表索引信息

mysql: [Warning] Using a password on the command line interface can be insecure.
Table	Non_unique	Key_name	Seq_in_index	Column_name	Collation	Cardinality	Sub_part	Packed	Null	Index_type	Comment	Index_comment	Visible	Expression
posts	0	PRIMARY	1	id	A	2	NULL	NULL		BTREE			YES	NULL
posts	1	idx_merchant_id	1	merchant_id	A	1	NULL	NULL		BTREE			YES	NULL
posts	1	idx_status	1	status	A	1	NULL	NULL		BTREE			YES	NULL
posts	1	idx_created_at	1	created_at	A	2	NULL	NULL		BTREE			YES	NULL

---

### conversations 表索引信息

mysql: [Warning] Using a password on the command line interface can be insecure.
Table	Non_unique	Key_name	Seq_in_index	Column_name	Collation	Cardinality	Sub_part	Packed	Null	Index_type	Comment	Index_comment	Visible	Expression
conversations	0	PRIMARY	1	id	A	4	NULL	NULL		BTREE			YES	NULL
conversations	0	uk_conversation_participant	1	participant_id	A	3	NULL	NULL		BTREE			YES	NULL
conversations	0	uk_conversation_participant	2	other_participant_id	A	4	NULL	NULL		BTREE			YES	NULL
conversations	1	idx_participant_id	1	participant_id	A	3	NULL	NULL		BTREE			YES	NULL
conversations	1	idx_last_message_time	1	last_message_time	A	2	NULL	NULL	YES	BTREE			YES	NULL

---

### messages 表索引信息

mysql: [Warning] Using a password on the command line interface can be insecure.
Table	Non_unique	Key_name	Seq_in_index	Column_name	Collation	Cardinality	Sub_part	Packed	Null	Index_type	Comment	Index_comment	Visible	Expression
messages	0	PRIMARY	1	id	A	12	NULL	NULL		BTREE			YES	NULL
messages	0	uk_client_msg_id	1	client_msg_id	A	12	NULL	NULL		BTREE			YES	NULL
messages	1	idx_user_pair	1	small_user_id	A	3	NULL	NULL		BTREE			YES	NULL
messages	1	idx_user_pair	2	big_user_id	A	3	NULL	NULL		BTREE			YES	NULL
messages	1	idx_logical_timestamp	1	client_msg_number	A	5	NULL	NULL	YES	BTREE			YES	NULL

---

### smart_reply_templates 表索引信息

mysql: [Warning] Using a password on the command line interface can be insecure.
Table	Non_unique	Key_name	Seq_in_index	Column_name	Collation	Cardinality	Sub_part	Packed	Null	Index_type	Comment	Index_comment	Visible	Expression
smart_reply_templates	0	PRIMARY	1	id	A	2	NULL	NULL		BTREE			YES	NULL
smart_reply_templates	1	idx_merchant_id	1	merchant_id	A	2	NULL	NULL		BTREE			YES	NULL

---

### merchant_applications 表索引信息

mysql: [Warning] Using a password on the command line interface can be insecure.
Table	Non_unique	Key_name	Seq_in_index	Column_name	Collation	Cardinality	Sub_part	Packed	Null	Index_type	Comment	Index_comment	Visible	Expression
merchant_applications	0	PRIMARY	1	id	A	1	NULL	NULL		BTREE			YES	NULL
merchant_applications	1	idx_user_id	1	user_id	A	1	NULL	NULL		BTREE			YES	NULL
merchant_applications	1	idx_status	1	status	A	1	NULL	NULL		BTREE			YES	NULL
merchant_applications	1	idx_applied_at	1	applied_at	A	1	NULL	NULL		BTREE			YES	NULL

---

### 1.1 帖子列表查询（用户端）

查询语句:
SELECT * FROM posts WHERE status = 1 ORDER BY created_at DESC LIMIT 20;

EXPLAIN结果:
mysql: [Warning] Using a password on the command line interface can be insecure.
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	posts	NULL	ref	idx_status	idx_status	1	const	1	100.00	Using filesort

---

### 1.2 我的帖子查询

查询语句:
SELECT * FROM posts WHERE merchant_id = 'test_merchant' AND status IN (1, -2, -3) ORDER BY created_at DESC LIMIT 20;

EXPLAIN结果:
mysql: [Warning] Using a password on the command line interface can be insecure.
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	posts	NULL	ref	idx_merchant_id,idx_status	idx_merchant_id	258	const	1	100.00	Using where; Using filesort

---

### 1.3 帖子详情查询

查询语句:
SELECT * FROM posts WHERE id = 'test_post_id';

EXPLAIN结果:
mysql: [Warning] Using a password on the command line interface can be insecure.
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	no matching row in const table

---

### 2.1 用户会话列表查询

查询语句:
SELECT * FROM conversations WHERE participant_id = 'test_user' AND is_deleted = 0 ORDER BY last_message_time DESC, created_at DESC LIMIT 20;

EXPLAIN结果:
mysql: [Warning] Using a password on the command line interface can be insecure.
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	conversations	NULL	ref	uk_conversation_participant,idx_participant_id	uk_conversation_participant	258	const	1	25.00	Using where; Using filesort

---

### 2.2 会话存在性检查

查询语句:
SELECT * FROM conversations WHERE participant_id = 'user1' AND other_participant_id = 'user2' AND is_deleted = 0 LIMIT 1;

EXPLAIN结果:
mysql: [Warning] Using a password on the command line interface can be insecure.
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	no matching row in const table

---

### 2.3 管理端商家会话查询

查询语句:
SELECT * FROM conversations WHERE participant_type = 2 AND participant_id = 'test_merchant' AND is_deleted = 0 ORDER BY last_message_time DESC LIMIT 20;

EXPLAIN结果:
mysql: [Warning] Using a password on the command line interface can be insecure.
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	conversations	NULL	ref	uk_conversation_participant,idx_participant_id	uk_conversation_participant	258	const	1	25.00	Using where; Using filesort

---

### 2.4 管理端客户会话查询

查询语句:
SELECT * FROM conversations WHERE participant_type = 2 AND other_participant_id = 'test_client' AND is_deleted = 0 ORDER BY last_message_time DESC LIMIT 20;

EXPLAIN结果:
mysql: [Warning] Using a password on the command line interface can be insecure.
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	conversations	NULL	ALL	NULL	NULL	NULL	NULL	4	25.00	Using where; Using filesort

---

### 3.1 消息列表查询

查询语句:
SELECT * FROM messages WHERE big_user_id = 'user_big' AND small_user_id = 'user_small' ORDER BY client_msg_number DESC, created_at DESC LIMIT 50;

EXPLAIN结果:
mysql: [Warning] Using a password on the command line interface can be insecure.
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	messages	NULL	ref	idx_user_pair	idx_user_pair	516	const,const	1	100.00	Using filesort

---

### 3.2 消息统计查询

查询语句:
SELECT COUNT(*) FROM messages WHERE big_user_id = 'user_big' AND small_user_id = 'user_small';

EXPLAIN结果:
mysql: [Warning] Using a password on the command line interface can be insecure.
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	messages	NULL	ref	idx_user_pair	idx_user_pair	516	const,const	1	100.00	Using index

---

### 3.3 频率限制查询

查询语句:
SELECT COUNT(*) FROM messages WHERE big_user_id = 'user_big' AND small_user_id = 'user_small' AND direction = 1 AND created_at > DATE_SUB(NOW(), INTERVAL 1 MINUTE);

EXPLAIN结果:
mysql: [Warning] Using a password on the command line interface can be insecure.
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	messages	NULL	ref	idx_user_pair	idx_user_pair	516	const,const	1	8.33	Using where

---

### 3.4 帖子相关消息查询

查询语句:
SELECT * FROM messages WHERE post_id = 'test_post_id' ORDER BY created_at DESC LIMIT 20;

EXPLAIN结果:
mysql: [Warning] Using a password on the command line interface can be insecure.
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	messages	NULL	ALL	NULL	NULL	NULL	NULL	12	10.00	Using where; Using filesort

---

### 4.1 商家申请状态查询

查询语句:
SELECT * FROM merchant_applications WHERE user_id = 'test_user';

EXPLAIN结果:
mysql: [Warning] Using a password on the command line interface can be insecure.
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	merchant_applications	NULL	ref	idx_user_id	idx_user_id	258	const	1	100.00	NULL

---

### 4.2 智能回复模板查询

查询语句:
SELECT * FROM smart_reply_templates WHERE merchant_id = 'test_merchant';

EXPLAIN结果:
mysql: [Warning] Using a password on the command line interface can be insecure.
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	smart_reply_templates	NULL	ref	idx_merchant_id	idx_merchant_id	258	const	1	100.00	NULL

---

### 4.3 管理端申请列表查询

查询语句:
SELECT * FROM merchant_applications WHERE applied_at BETWEEN '2024-01-01' AND '2024-12-31' ORDER BY applied_at DESC LIMIT 20;

EXPLAIN结果:
mysql: [Warning] Using a password on the command line interface can be insecure.
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	merchant_applications	NULL	range	idx_applied_at	idx_applied_at	5	NULL	1	100.00	Using index condition; Backward index scan

---

=== 分析总结 ===

本次分析验证了已创建索引的实际效果：

核心查询分析：
1. 帖子列表查询 - 验证 idx_status_created_at 索引效果
2. 我的帖子查询 - 验证 idx_merchant_status_created_at 索引效果  
3. 会话列表查询 - 验证 idx_participant_deleted_time 索引效果
4. 消息列表查询 - 验证 idx_users_order 索引效果
5. 其他重要查询 - 验证各种辅助索引效果

关键性能指标：
- type: 应该显示 ref/range 而不是 ALL
- key: 应该显示对应的索引名称
- rows: 扫描行数应该大幅减少
- Extra: 应该没有 "Using filesort" 

索引使用情况：
- 如果 key 字段显示了索引名，说明索引被正确使用
- 如果 type 是 ref/range，说明索引查找效率高
- 如果 Extra 没有 filesort，说明利用了索引排序

优化建议：
1. 监控实际查询性能，确保索引发挥作用
2. 定期检查索引使用率，删除未使用的索引
3. 根据业务发展调整索引策略
4. 关注写入性能，平衡查询和写入效率

