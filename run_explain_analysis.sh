#!/bin/bash

# 卡牌集社模块MySQL查询性能分析脚本
# 使用方法: ./run_explain_analysis.sh

# MySQL连接参数 - 请根据实际情况修改
MYSQL_HOST="127.0.0.1"
MYSQL_USER="root"
MYSQL_PASSWORD="123456"  # 请填入实际密码
MYSQL_DATABASE="app_service"  # 请填入实际数据库名

# 输出文件
OUTPUT_FILE="explain_analysis_results.txt"
TIMESTAMP=$(date '+%Y-%m-%d %H:%M:%S')

# 检查参数
if [ -z "$MYSQL_PASSWORD" ] || [ -z "$MYSQL_DATABASE" ]; then
    echo "请先修改脚本中的MySQL连接参数："
    echo "- MYSQL_PASSWORD: MySQL密码"
    echo "- MYSQL_DATABASE: 数据库名"
    exit 1
fi

# 初始化输出文件
cat > $OUTPUT_FILE << EOF
=== 卡牌集社模块查询性能分析 ===
分析时间: $TIMESTAMP
数据库: $MYSQL_DATABASE
主机: $MYSQL_HOST

EOF

# 函数：执行EXPLAIN并记录结果
run_explain() {
    local query_name="$1"
    local sql_query="$2"
    local is_optimized="$3"

    echo "正在分析: $query_name"

    cat >> $OUTPUT_FILE << EOF
### $query_name

查询语句:
$sql_query

EXPLAIN结果:
EOF

    # 执行EXPLAIN查询
    mysql -h$MYSQL_HOST -u$MYSQL_USER -p$MYSQL_PASSWORD $MYSQL_DATABASE \
        -e "EXPLAIN FORMAT=TRADITIONAL $sql_query" >> $OUTPUT_FILE 2>&1

    if [ $? -eq 0 ]; then
        echo "✓ $query_name 分析完成"
    else
        echo "✗ $query_name 分析失败"
    fi

    cat >> $OUTPUT_FILE << EOF

---

EOF
}

# 函数：执行SQL语句（用于添加索引）
execute_sql() {
    local sql="$1"
    local description="$2"

    echo "正在执行: $description"
    mysql -h$MYSQL_HOST -u$MYSQL_USER -p$MYSQL_PASSWORD $MYSQL_DATABASE -e "$sql" 2>/dev/null

    if [ $? -eq 0 ]; then
        echo "✓ $description 执行成功"
    else
        echo "⚠ $description 执行失败（可能索引已存在）"
    fi
}

echo "开始MySQL查询性能分析..."
echo "结果将保存到: $OUTPUT_FILE"
echo ""

# 1. 帖子列表查询分析
echo "=== 1. 帖子列表查询分析 ==="

# 优化前
run_explain "1.1 帖子列表查询（优化前）" \
    "SELECT * FROM posts WHERE status = 1 ORDER BY created_at DESC LIMIT 20;"

# 添加索引
execute_sql "ALTER TABLE posts ADD INDEX idx_status_created_at (status, created_at DESC);" \
    "添加帖子状态+时间索引"

# 优化后
run_explain "1.2 帖子列表查询（优化后）" \
    "SELECT * FROM posts WHERE status = 1 ORDER BY created_at DESC LIMIT 20;"

echo ""

# 2. 我的帖子查询分析
echo "=== 2. 我的帖子查询分析 ==="

# 优化前
run_explain "2.1 我的帖子查询（优化前）" \
    "SELECT * FROM posts WHERE merchant_id = 'test_merchant' AND status IN (1, -2, -3) ORDER BY created_at DESC LIMIT 20;"

# 添加索引
execute_sql "ALTER TABLE posts ADD INDEX idx_merchant_status_created_at (merchant_id, status, created_at DESC);" \
    "添加商家+状态+时间索引"

# 优化后
run_explain "2.2 我的帖子查询（优化后）" \
    "SELECT * FROM posts WHERE merchant_id = 'test_merchant' AND status IN (1, -2, -3) ORDER BY created_at DESC LIMIT 20;"

echo ""

# 3. 会话列表查询分析
echo "=== 3. 会话列表查询分析 ==="

# 优化前
run_explain "3.1 会话列表查询（优化前）" \
    "SELECT * FROM conversations WHERE participant_id = 'test_user' AND is_deleted = 0 ORDER BY last_message_time DESC, created_at DESC LIMIT 20;"

# 添加索引
execute_sql "ALTER TABLE conversations ADD INDEX idx_participant_deleted_time (participant_id, is_deleted, last_message_time DESC, created_at DESC);" \
    "添加参与者+删除状态+时间索引"

# 优化后
run_explain "3.2 会话列表查询（优化后）" \
    "SELECT * FROM conversations WHERE participant_id = 'test_user' AND is_deleted = 0 ORDER BY last_message_time DESC, created_at DESC LIMIT 20;"

# 4. 消息列表查询分析
echo "=== 4. 消息列表查询分析 ==="

# 优化前
run_explain "4.1 消息列表查询（优化前）" \
    "SELECT * FROM messages WHERE big_user_id = 'user_big' AND small_user_id = 'user_small' ORDER BY client_msg_number DESC, created_at DESC LIMIT 50;"

# 添加索引
execute_sql "ALTER TABLE messages ADD INDEX idx_users_order (big_user_id, small_user_id, client_msg_number DESC, created_at DESC);" \
    "添加用户对+消息序号+时间索引"

# 优化后
run_explain "4.2 消息列表查询（优化后）" \
    "SELECT * FROM messages WHERE big_user_id = 'user_big' AND small_user_id = 'user_small' ORDER BY client_msg_number DESC, created_at DESC LIMIT 50;"

echo ""

# 5. 其他重要查询分析
echo "=== 5. 其他重要查询分析 ==="

# 会话存在性检查
run_explain "5.1 会话存在性检查" \
    "SELECT * FROM conversations WHERE participant_id = 'user1' AND other_participant_id = 'user2' AND is_deleted = 0 LIMIT 1;"

# 消息频率限制查询
execute_sql "ALTER TABLE messages ADD INDEX idx_users_direction (big_user_id, small_user_id, direction);" \
    "添加用户对+方向索引"

run_explain "5.2 消息频率限制查询" \
    "SELECT COUNT(*) FROM messages WHERE big_user_id = 'user_big' AND small_user_id = 'user_small' AND direction = 1 AND created_at > DATE_SUB(NOW(), INTERVAL 1 MINUTE);"

# 商家申请状态查询
run_explain "5.3 商家申请状态查询" \
    "SELECT * FROM merchant_applications WHERE user_id = 'test_user' LIMIT 1;"

echo ""

# 生成总结
cat >> $OUTPUT_FILE << EOF
=== 分析总结 ===

本次分析涵盖了卡牌集社模块的核心用户端查询：

1. 帖子列表查询 - 用户浏览帖子的主要查询
2. 我的帖子查询 - 商家管理自己帖子的查询
3. 会话列表查询 - 用户查看聊天会话的查询
4. 消息列表查询 - 用户查看聊天消息的查询
5. 其他重要查询 - 会话检查、频率限制等

关键优化指标：
- type: 从 ALL 改为 ref/range 表示索引生效
- rows: 扫描行数大幅减少
- Extra: 去掉 "Using filesort" 表示利用索引排序
- key: 显示使用了新建的索引

预期性能提升：
- 查询响应时间从毫秒级降至微秒级
- 支持更高的并发查询
- 减少数据库CPU和IO负载
- 提升用户体验

注意事项：
- 索引会占用额外存储空间
- 写入操作会有轻微性能影响
- 需要定期维护和监控索引使用情况

EOF

echo "=== 分析完成 ==="
echo "详细结果已保存到: $OUTPUT_FILE"
echo ""
echo "建议："
echo "1. 查看 $OUTPUT_FILE 了解详细的EXPLAIN结果"
echo "2. 对比优化前后的 type、rows、Extra 字段变化"
echo "3. 在生产环境应用索引前，请先在测试环境验证"
echo "4. 监控索引应用后的实际性能表现"