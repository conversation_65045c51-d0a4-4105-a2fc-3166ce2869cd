# 卡牌集社模块用户端查询EXPLAIN分析文档

## 概述

本文档分析卡牌集社模块用户端主要查询的执行计划，验证索引设计的有效性。

## 分析的查询场景

### 1. 帖子相关查询

#### 1.1 用户端帖子列表查询
```sql
-- 查询语句
EXPLAIN SELECT * FROM posts 
WHERE status = 1 
ORDER BY created_at DESC 
LIMIT 20;

-- 预期执行计划
+----+-------------+-------+------+----------------------+----------------------+---------+-------+------+-------+
| id | select_type | table | type | possible_keys        | key                  | key_len | ref   | rows | Extra |
+----+-------------+-------+------+----------------------+----------------------+---------+-------+------+-------+
|  1 | SIMPLE      | posts | ref  | idx_status_created_at| idx_status_created_at| 1       | const |  100 |       |
+----+-------------+-------+------+----------------------+----------------------+---------+-------+------+-------+

-- 分析结果
- type: ref (使用索引进行等值查询)
- key: idx_status_created_at (使用新增的复合索引)
- Extra: 无Using filesort (索引已包含排序，无需额外排序)
```

#### 1.2 我的帖子查询
```sql
-- 查询语句
EXPLAIN SELECT * FROM posts 
WHERE merchant_id = 'user123' AND status IN (1, -2, -3) 
ORDER BY created_at DESC 
LIMIT 20;

-- 预期执行计划
+----+-------------+-------+-------+--------------------------------+--------------------------------+---------+------+------+-------+
| id | select_type | table | type  | possible_keys                  | key                            | key_len | ref  | rows | Extra |
+----+-------------+-------+-------+--------------------------------+--------------------------------+---------+------+------+-------+
|  1 | SIMPLE      | posts | range | idx_merchant_status_created_at | idx_merchant_status_created_at | 66      | NULL |   10 |       |
+----+-------------+-------+-------+--------------------------------+--------------------------------+---------+------+------+-------+

-- 分析结果
- type: range (使用索引进行范围查询)
- key: idx_merchant_status_created_at (使用新增的复合索引)
- Extra: 无Using filesort (索引已包含排序)
```

#### 1.3 帖子详情查询
```sql
-- 查询语句
EXPLAIN SELECT * FROM posts WHERE id = 'post123';

-- 预期执行计划
+----+-------------+-------+-------+---------------+---------+---------+-------+------+-------+
| id | select_type | table | type  | possible_keys | key     | key_len | ref   | rows | Extra |
+----+-------------+-------+-------+---------------+---------+---------+-------+------+-------+
|  1 | SIMPLE      | posts | const | PRIMARY       | PRIMARY | 194     | const |    1 |       |
+----+-------------+-------+-------+---------------+---------+---------+-------+------+-------+

-- 分析结果
- type: const (主键查询，最优性能)
- rows: 1 (精确匹配一条记录)
```

### 2. 会话相关查询

#### 2.1 用户会话列表查询
```sql
-- 查询语句
EXPLAIN SELECT * FROM conversations 
WHERE participant_id = 'user123' AND is_deleted = 0 
ORDER BY last_message_time DESC, created_at DESC 
LIMIT 20;

-- 预期执行计划
+----+-------------+---------------+------+-----------------------------+-----------------------------+---------+-------------+------+-------+
| id | select_type | table         | type | possible_keys               | key                         | key_len | ref         | rows | Extra |
+----+-------------+---------------+------+-----------------------------+-----------------------------+---------+-------------+------+-------+
|  1 | SIMPLE      | conversations | ref  | idx_participant_deleted_time| idx_participant_deleted_time| 195     | const,const |   50 |       |
+----+-------------+---------------+------+-----------------------------+-----------------------------+---------+-------------+------+-------+

-- 分析结果
- type: ref (使用复合索引进行等值查询)
- key: idx_participant_deleted_time (使用新增的复合索引)
- Extra: 无Using filesort (索引包含排序字段)
```

#### 2.2 会话存在性检查
```sql
-- 查询语句
EXPLAIN SELECT * FROM conversations 
WHERE participant_id = 'user123' 
  AND other_participant_id = 'user456' 
  AND is_deleted = 0;

-- 预期执行计划
+----+-------------+---------------+-------+---------------------------+---------------------------+---------+-------------+------+-------+
| id | select_type | table         | type  | possible_keys             | key                       | key_len | ref         | rows | Extra |
+----+-------------+---------------+-------+---------------------------+---------------------------+---------+-------------+------+-------+
|  1 | SIMPLE      | conversations | const | uk_conversation_participant| uk_conversation_participant| 388     | const,const |    1 |       |
+----+-------------+---------------+-------+---------------------------+---------------------------+---------+-------------+------+-------+

-- 分析结果
- type: const (唯一索引查询，最优性能)
- key: uk_conversation_participant (使用唯一索引)
- rows: 1 (唯一匹配)
```

#### 2.3 创建会话前的检查
```sql
-- 查询语句
EXPLAIN SELECT * FROM conversations 
WHERE participant_id = 'user123' 
  AND other_participant_id = 'merchant456' 
  AND is_deleted = 0;

-- 预期执行计划
+----+-------------+---------------+-------+---------------------------+---------------------------+---------+-------------+------+-------+
| id | select_type | table         | type  | possible_keys             | key                       | key_len | ref         | rows | Extra |
+----+-------------+---------------+-------+---------------------------+---------------------------+---------+-------------+------+-------+
|  1 | SIMPLE      | conversations | const | uk_conversation_participant| uk_conversation_participant| 388     | const,const |    1 |       |
+----+-------------+---------------+-------+---------------------------+---------------------------+---------+-------------+------+-------+

-- 分析结果
- type: const (唯一索引查询)
- key: uk_conversation_participant (现有唯一索引)
```

### 3. 消息相关查询

#### 3.1 消息列表查询
```sql
-- 查询语句
EXPLAIN SELECT * FROM messages 
WHERE big_user_id = 'user456' AND small_user_id = 'user123' 
ORDER BY client_msg_number DESC, created_at DESC 
LIMIT 50;

-- 预期执行计划
+----+-------------+----------+------+----------------+----------------+---------+-------------+------+-------+
| id | select_type | table    | type | possible_keys  | key            | key_len | ref         | rows | Extra |
+----+-------------+----------+------+----------------+----------------+---------+-------------+------+-------+
|  1 | SIMPLE      | messages | ref  | idx_users_order| idx_users_order| 388     | const,const |  200 |       |
+----+-------------+----------+------+----------------+----------------+---------+-------------+------+-------+

-- 分析结果
- type: ref (使用复合索引进行等值查询)
- key: idx_users_order (使用新增的复合索引)
- Extra: 无Using filesort (索引包含排序字段)
```

#### 3.2 消息统计查询
```sql
-- 查询语句
EXPLAIN SELECT COUNT(*) FROM messages 
WHERE big_user_id = 'user456' AND small_user_id = 'user123';

-- 预期执行计划
+----+-------------+----------+------+----------------+----------------+---------+-------------+------+--------------+
| id | select_type | table    | type | possible_keys  | key            | key_len | ref         | rows | Extra        |
+----+-------------+----------+------+----------------+----------------+---------+-------------+------+--------------+
|  1 | SIMPLE      | messages | ref  | idx_users_order| idx_users_order| 388     | const,const |  200 | Using index  |
+----+-------------+----------+------+----------------+----------------+---------+-------------+------+--------------+

-- 分析结果
- type: ref (使用索引)
- key: idx_users_order (复用消息列表索引)
- Extra: Using index (覆盖索引，无需回表)
```

### 4. 商家申请相关查询

#### 4.1 获取申请状态
```sql
-- 查询语句
EXPLAIN SELECT * FROM merchant_applications 
WHERE user_id = 'user123';

-- 预期执行计划
+----+-------------+----------------------+-------+---------------+------------+---------+-------+------+-------+
| id | select_type | table                | type  | possible_keys | key        | key_len | ref   | rows | Extra |
+----+-------------+----------------------+-------+---------------+------------+---------+-------+------+-------+
|  1 | SIMPLE      | merchant_applications| const | uk_user_id    | uk_user_id | 194     | const |    1 |       |
+----+-------------+----------------------+-------+---------------+------------+---------+-------+------+-------+

-- 分析结果
- type: const (唯一索引查询，最优性能)
- key: uk_user_id (使用唯一索引)
- rows: 1 (唯一匹配)
```

### 5. 智能回复模板查询

#### 5.1 获取商家模板
```sql
-- 查询语句
EXPLAIN SELECT * FROM smart_reply_templates 
WHERE merchant_id = 'merchant123';

-- 预期执行计划
+----+-------------+----------------------+------+----------------+----------------+---------+-------+------+-------+
| id | select_type | table                | type | possible_keys  | key            | key_len | ref   | rows | Extra |
+----+-------------+----------------------+------+----------------+----------------+---------+-------+------+-------+
|  1 | SIMPLE      | smart_reply_templates| ref  | idx_merchant_id| idx_merchant_id| 194     | const |    1 |       |
+----+-------------+----------------------+------+----------------+----------------+---------+-------+------+-------+

-- 分析结果
- type: ref (使用索引进行等值查询)
- key: idx_merchant_id (使用现有索引)
- rows: 1 (预期一条记录)
```

## 性能优化效果总结

### 1. 查询性能提升

| 查询场景 | 优化前 | 优化后 | 提升效果 |
|---------|--------|--------|----------|
| 帖子列表 | 全表扫描+排序 | 索引扫描 | 10-50倍 |
| 我的帖子 | 全表扫描+排序 | 索引范围扫描 | 20-100倍 |
| 会话列表 | 全表扫描+排序 | 索引扫描 | 10-30倍 |
| 消息列表 | 全表扫描+排序 | 索引扫描 | 50-200倍 |
| 申请状态 | 全表扫描 | 唯一索引 | 100-1000倍 |

### 2. 索引使用率

- **高频使用索引**：
  - `idx_users_order`: 消息查询核心索引
  - `idx_participant_deleted_time`: 会话列表核心索引
  - `idx_status_created_at`: 帖子列表核心索引

- **中频使用索引**：
  - `idx_merchant_status_created_at`: 我的帖子查询
  - `uk_user_id`: 商家申请状态查询

### 3. 排序优化

所有主要查询都通过索引避免了 `Using filesort`，大幅提升排序性能：
- 帖子按时间倒序：通过 `created_at DESC` 索引字段
- 会话按时间倒序：通过 `last_message_time DESC, created_at DESC` 索引字段  
- 消息按序号倒序：通过 `client_msg_number DESC, created_at DESC` 索引字段

## 监控建议

### 1. 关键指标监控
- 慢查询日志：监控执行时间 > 100ms 的查询
- 索引使用率：定期检查 `SHOW INDEX` 统计信息
- 查询计划变化：监控 EXPLAIN 结果的稳定性

### 2. 性能基准
- 帖子列表查询：< 50ms
- 会话列表查询：< 100ms  
- 消息列表查询：< 50ms
- 详情查询：< 10ms

### 3. 容量规划
- 单表数据量达到 100万 时重新评估索引效果
- 索引空间占用不超过数据空间的 30%
- 定期进行 `ANALYZE TABLE` 更新统计信息
