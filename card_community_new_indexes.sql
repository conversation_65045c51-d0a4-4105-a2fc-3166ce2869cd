-- 卡牌集社模块新增索引SQL语句
-- 基于查询分析结果，为现有表添加性能优化索引

-- ========================================
-- Posts表新增索引
-- ========================================

-- 用户端帖子列表查询优化（status + created_at倒序）
-- 查询：WHERE status = 1 ORDER BY created_at DESC
ALTER TABLE `posts` ADD INDEX `idx_status_created_at` (`status`, `created_at` DESC);

-- 我的帖子查询优化（merchant_id + status + created_at倒序）
-- 查询：WHERE merchant_id = ? AND status IN (...) ORDER BY created_at DESC
ALTER TABLE `posts` ADD INDEX `idx_merchant_status_created_at` (`merchant_id`, `status`, `created_at` DESC);

-- 管理端描述搜索优化
-- 查询：WHERE description LIKE '%keyword%'
-- ALTER TABLE `posts` ADD INDEX `idx_description` (`description`(100));

-- 删除Posts表的多余索引
-- idx_status 被 idx_status_created_at 覆盖，可删除
ALTER TABLE `posts` DROP INDEX `idx_status`;
-- idx_merchant_id 被 idx_merchant_status_created_at 覆盖，可删除
ALTER TABLE `posts` DROP INDEX `idx_merchant_id`;

-- ========================================
-- Conversations表新增索引
-- ========================================

-- 会话列表查询优化（participant_id + is_deleted + 排序）
-- 查询：WHERE participant_id = ? AND is_deleted = 0 ORDER BY last_message_time DESC, created_at DESC
-- 注意：is_deleted字段目前都是0，但为未来删除会话功能预留
ALTER TABLE `conversations` ADD INDEX `idx_participant_deleted_time` (`participant_id`, `is_deleted`, `last_message_time` DESC, `created_at` DESC);

-- 管理端商家会话查询优化
-- 查询：WHERE participant_type = 2 AND participant_id = ? AND is_deleted = 0
-- 简化版：去掉is_deleted字段，因为数据量小，过滤成本低
ALTER TABLE `conversations` ADD INDEX `idx_type_participant` (`participant_type`, `participant_id`);

-- 管理端客户会话查询优化
-- 查询：WHERE participant_type = 2 AND other_participant_id = ? AND is_deleted = 0
-- 简化版：去掉is_deleted字段，因为数据量小，过滤成本低
ALTER TABLE `conversations` ADD INDEX `idx_type_other_participant` (`participant_type`, `other_participant_id`);

-- 可选方案：如果确定不需要删除会话功能，可以考虑以下简化索引
-- ALTER TABLE `conversations` ADD INDEX `idx_participant_time` (`participant_id`, `last_message_time` DESC, `created_at` DESC);

-- 添加缺失字段（如果不存在）
-- ALTER TABLE `conversations` ADD COLUMN `status` tinyint NOT NULL DEFAULT 1 COMMENT '状态：1=正常 -1=限制中';
-- ALTER TABLE `conversations` ADD COLUMN `post_id` varchar(64) DEFAULT NULL COMMENT '关联的帖子ID';

-- ========================================
-- Messages表新增索引
-- ========================================

-- 消息列表查询优化（big_user_id + small_user_id + 排序）
-- 查询：WHERE big_user_id = ? AND small_user_id = ? ORDER BY client_msg_number DESC, created_at DESC
ALTER TABLE `messages` ADD INDEX `idx_users_order` (`big_user_id`, `small_user_id`, `client_msg_number` DESC, `created_at` DESC);

-- 频率限制查询优化（big_user_id + small_user_id + direction）
-- 查询：WHERE big_user_id = ? AND small_user_id = ? AND direction = ?
ALTER TABLE `messages` ADD INDEX `idx_users_direction` (`big_user_id`, `small_user_id`, `direction`);

-- 帖子相关消息查询优化
-- 查询：WHERE post_id = ?
ALTER TABLE `messages` ADD INDEX `idx_post_id` (`post_id`);

-- 管理端内容搜索优化
-- 查询：WHERE content LIKE '%keyword%'
-- ALTER TABLE `messages` ADD INDEX `idx_content` (`content`(100));

-- ========================================
-- SmartReplyTemplates表新增索引
-- ========================================

-- 商家模板唯一性约束（每个商家只能有一个模板）
-- 查询：WHERE merchant_id = ?
ALTER TABLE `smart_reply_templates` ADD UNIQUE INDEX `uk_merchant_id` (`merchant_id`);

-- 删除原有的普通索引（如果存在唯一索引后不再需要）
ALTER TABLE `smart_reply_templates` DROP INDEX `idx_merchant_id`;

-- ========================================
-- MerchantApplications表新增索引
-- ========================================

-- 商家状态检查优化（user_id + status）
-- 查询：WHERE user_id = ? AND status = 2
-- 注意：由于uk_user_id唯一约束，每个用户只有一条记录，此索引也可能不需要
ALTER TABLE `merchant_applications` ADD INDEX `idx_user_status` (`user_id`, `status`);

-- 用户申请历史查询优化（user_id + applied_at倒序）
-- 查询：WHERE user_id = ? ORDER BY applied_at DESC
-- 不需要此索引：uk_user_id唯一约束保证每个用户只有一条记录，排序无意义
-- ALTER TABLE `merchant_applications` ADD INDEX `idx_user_applied_at` (`user_id`, `applied_at` DESC);

-- 管理端时间范围查询优化
-- 查询：WHERE applied_at BETWEEN ? AND ?
ALTER TABLE `merchant_applications` ADD INDEX `idx_applied_at` (`applied_at`);

-- ========================================
-- 删除多余索引
-- ========================================

-- Messages表：删除被覆盖的索引
-- idx_user_pair 被 idx_users_order 完全覆盖，可以删除
ALTER TABLE `messages` DROP INDEX `idx_user_pair`;

-- Conversations表：删除被覆盖的索引
-- idx_participant_id 被 idx_participant_deleted_time 覆盖
ALTER TABLE `conversations` DROP INDEX `idx_participant_id`;
-- idx_last_message_time 被 idx_participant_deleted_time 覆盖
ALTER TABLE `conversations` DROP INDEX `idx_last_message_time`;

-- ========================================
-- 索引使用说明
-- ========================================

/*
索引优化效果说明：

1. Posts表：
   - idx_status_created_at: 支持用户端帖子列表的高效查询和排序
   - idx_merchant_status_created_at: 支持"我的帖子"页面的复合条件查询

2. Conversations表：
   - idx_participant_deleted_time: 支持会话列表的高效查询和排序
   - idx_type_participant/idx_type_other_participant: 支持管理端的会话筛选

3. Messages表：
   - idx_users_order: 支持消息列表的高效查询和排序（最重要的索引）
   - idx_users_direction: 支持频率限制功能的查询
   - idx_post_id: 支持帖子相关消息的查询

4. SmartReplyTemplates表：
   - uk_merchant_id: 确保每个商家只能有一个智能回复模板

5. MerchantApplications表：
   - idx_user_status: 支持商家状态检查（可选，因为有唯一约束）
   - idx_applied_at: 支持管理端的时间范围筛选

多余索引分析：
1. Posts表的 idx_status 被 idx_status_created_at 覆盖，idx_merchant_id 被 idx_merchant_status_created_at 覆盖
2. Messages表的 idx_user_pair 被 idx_users_order 完全覆盖
3. SmartReplyTemplates表的 idx_merchant_id 被 uk_merchant_id 完全覆盖
4. Conversations表的 idx_participant_id 和 idx_last_message_time 被 idx_participant_deleted_time 覆盖

is_deleted字段说明：
- 目前所有查询都使用 is_deleted = 0，但没有删除会话的功能实现
- 该字段是为未来"删除联系人"功能预留的设计
- 如果确定不需要删除会话功能，可以考虑使用简化版索引（见上方注释）

保留的现有索引说明：
1. Posts表的 idx_created_at 需要保留，因为管理端有单独按时间范围查询的场景
2. MerchantApplications表的 idx_status 需要保留，因为管理端有单独按状态筛选的场景
3. Conversations表的 uk_conversation_participant 需要保留，用于会话存在性检查

注意事项：
- 执行前请备份数据库
- 建议在业务低峰期执行
- 大表添加索引可能需要较长时间
- 删除索引前确认没有其他查询依赖
- 索引会占用额外存储空间，但能显著提升查询性能
*/
