#!/bin/bash

# 卡牌集社模块MySQL查询性能分析脚本（索引已存在版本）
# 使用方法: ./analyze_existing_indexes.sh

# MySQL连接参数 - 请根据实际情况修改
MYSQL_HOST="127.0.0.1"
MYSQL_USER="root"
MYSQL_PASSWORD="123456"  # 请填入实际密码
MYSQL_DATABASE="app_service"  # 请填入实际数据库名

# 输出文件
OUTPUT_FILE="existing_indexes_analysis.txt"
TIMESTAMP=$(date '+%Y-%m-%d %H:%M:%S')

# 检查参数
if [ -z "$MYSQL_PASSWORD" ] || [ -z "$MYSQL_DATABASE" ]; then
    echo "请先修改脚本中的MySQL连接参数："
    echo "- MYSQL_PASSWORD: MySQL密码"
    echo "- MYSQL_DATABASE: 数据库名"
    exit 1
fi

# 初始化输出文件
cat > $OUTPUT_FILE << EOF
=== 卡牌集社模块查询性能分析（索引已存在） ===
分析时间: $TIMESTAMP
数据库: $MYSQL_DATABASE
主机: $MYSQL_HOST

EOF

# 函数：执行EXPLAIN并记录结果
run_explain() {
    local query_name="$1"
    local sql_query="$2"
    
    echo "正在分析: $query_name"
    
    cat >> $OUTPUT_FILE << EOF
### $query_name

查询语句:
$sql_query

EXPLAIN结果:
EOF
    
    # 执行EXPLAIN查询
    mysql -h$MYSQL_HOST -u$MYSQL_USER -p$MYSQL_PASSWORD $MYSQL_DATABASE \
        -e "EXPLAIN FORMAT=TRADITIONAL $sql_query" >> $OUTPUT_FILE 2>&1
    
    if [ $? -eq 0 ]; then
        echo "✓ $query_name 分析完成"
    else
        echo "✗ $query_name 分析失败"
    fi
    
    cat >> $OUTPUT_FILE << EOF

---

EOF
}

# 函数：显示表的索引信息
show_indexes() {
    local table_name="$1"
    
    echo "正在获取 $table_name 表的索引信息"
    
    cat >> $OUTPUT_FILE << EOF
### $table_name 表索引信息

EOF
    
    mysql -h$MYSQL_HOST -u$MYSQL_USER -p$MYSQL_PASSWORD $MYSQL_DATABASE \
        -e "SHOW INDEX FROM $table_name;" >> $OUTPUT_FILE 2>&1
    
    cat >> $OUTPUT_FILE << EOF

---

EOF
}

echo "开始MySQL查询性能分析（索引已存在）..."
echo "结果将保存到: $OUTPUT_FILE"
echo ""

# 显示各表的索引信息
echo "=== 获取表索引信息 ==="
show_indexes "posts"
show_indexes "conversations" 
show_indexes "messages"
show_indexes "smart_reply_templates"
show_indexes "merchant_applications"
echo ""

# 1. 帖子相关查询分析
echo "=== 1. 帖子相关查询分析 ==="

run_explain "1.1 帖子列表查询（用户端）" \
    "SELECT * FROM posts WHERE status = 1 ORDER BY created_at DESC LIMIT 20;"

run_explain "1.2 我的帖子查询" \
    "SELECT * FROM posts WHERE merchant_id = 'test_merchant' AND status IN (1, -2, -3) ORDER BY created_at DESC LIMIT 20;"

run_explain "1.3 帖子详情查询" \
    "SELECT * FROM posts WHERE id = 'test_post_id';"

echo ""

# 2. 会话相关查询分析  
echo "=== 2. 会话相关查询分析 ==="

run_explain "2.1 用户会话列表查询" \
    "SELECT * FROM conversations WHERE participant_id = 'test_user' AND is_deleted = 0 ORDER BY last_message_time DESC, created_at DESC LIMIT 20;"

run_explain "2.2 会话存在性检查" \
    "SELECT * FROM conversations WHERE participant_id = 'user1' AND other_participant_id = 'user2' AND is_deleted = 0 LIMIT 1;"

run_explain "2.3 管理端商家会话查询" \
    "SELECT * FROM conversations WHERE participant_type = 2 AND participant_id = 'test_merchant' AND is_deleted = 0 ORDER BY last_message_time DESC LIMIT 20;"

run_explain "2.4 管理端客户会话查询" \
    "SELECT * FROM conversations WHERE participant_type = 2 AND other_participant_id = 'test_client' AND is_deleted = 0 ORDER BY last_message_time DESC LIMIT 20;"

echo ""

# 3. 消息相关查询分析
echo "=== 3. 消息相关查询分析 ==="

run_explain "3.1 消息列表查询" \
    "SELECT * FROM messages WHERE big_user_id = 'user_big' AND small_user_id = 'user_small' ORDER BY client_msg_number DESC, created_at DESC LIMIT 50;"

run_explain "3.2 消息统计查询" \
    "SELECT COUNT(*) FROM messages WHERE big_user_id = 'user_big' AND small_user_id = 'user_small';"

run_explain "3.3 频率限制查询" \
    "SELECT COUNT(*) FROM messages WHERE big_user_id = 'user_big' AND small_user_id = 'user_small' AND direction = 1 AND created_at > DATE_SUB(NOW(), INTERVAL 1 MINUTE);"

run_explain "3.4 帖子相关消息查询" \
    "SELECT * FROM messages WHERE post_id = 'test_post_id' ORDER BY created_at DESC LIMIT 20;"

echo ""

# 4. 其他查询分析
echo "=== 4. 其他查询分析 ==="

run_explain "4.1 商家申请状态查询" \
    "SELECT * FROM merchant_applications WHERE user_id = 'test_user';"

run_explain "4.2 智能回复模板查询" \
    "SELECT * FROM smart_reply_templates WHERE merchant_id = 'test_merchant';"

run_explain "4.3 管理端申请列表查询" \
    "SELECT * FROM merchant_applications WHERE applied_at BETWEEN '2024-01-01' AND '2024-12-31' ORDER BY applied_at DESC LIMIT 20;"

echo ""

# 生成总结
cat >> $OUTPUT_FILE << EOF
=== 分析总结 ===

本次分析验证了已创建索引的实际效果：

核心查询分析：
1. 帖子列表查询 - 验证 idx_status_created_at 索引效果
2. 我的帖子查询 - 验证 idx_merchant_status_created_at 索引效果  
3. 会话列表查询 - 验证 idx_participant_deleted_time 索引效果
4. 消息列表查询 - 验证 idx_users_order 索引效果
5. 其他重要查询 - 验证各种辅助索引效果

关键性能指标：
- type: 应该显示 ref/range 而不是 ALL
- key: 应该显示对应的索引名称
- rows: 扫描行数应该大幅减少
- Extra: 应该没有 "Using filesort" 

索引使用情况：
- 如果 key 字段显示了索引名，说明索引被正确使用
- 如果 type 是 ref/range，说明索引查找效率高
- 如果 Extra 没有 filesort，说明利用了索引排序

优化建议：
1. 监控实际查询性能，确保索引发挥作用
2. 定期检查索引使用率，删除未使用的索引
3. 根据业务发展调整索引策略
4. 关注写入性能，平衡查询和写入效率

EOF

echo "=== 分析完成 ==="
echo "详细结果已保存到: $OUTPUT_FILE"
echo ""
echo "建议："
echo "1. 查看 $OUTPUT_FILE 了解详细的EXPLAIN结果"
echo "2. 重点关注 type、key、rows、Extra 字段"
echo "3. 验证关键查询是否使用了预期的索引"
echo "4. 监控生产环境的实际查询性能"
